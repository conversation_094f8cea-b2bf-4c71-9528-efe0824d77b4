import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/config/app_routes.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';
import 'package:inventory_app_final/core/widgets/custom_icon_button.dart';

class MainFunctionArea extends StatelessWidget {
  const MainFunctionArea({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(8.w),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.w),
        color: Colors.white,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        children: [
          Column(
            children: [
              CustomIconButton(
                size: 50.w,
                iconColor: AppColors.outBoundColor,
                onTap: () {
                  Navigator.pushNamed(context, AppRoutes.outbound);
                },
                icon: Icons.outbox_rounded,
              ),
              SizedBox(height: 3.h),
              Text("出库"),
            ],
          ),
          Column(
            children: [
              CustomIconButton(
                size: 50.w,
                iconColor: AppColors.inBoundColor,
                onTap: () {},
                icon: Icons.move_to_inbox_rounded,
              ),
              SizedBox(height: 3.h),
              Text("入库"),
            ],
          ),
          Column(
            children: [
              CustomIconButton(
                size: 50.w,
                iconColor: AppColors.addColor,
                onTap: () {},
                icon: Icons.add_box_rounded,
              ),
              SizedBox(height: 3.h),
              Text("添加"),
            ],
          ),
          Column(
            children: [
              CustomIconButton(
                size: 50.w,
                iconColor: AppColors.editColor,
                onTap: () {},
                icon: Icons.data_thresholding_rounded,
              ),
              SizedBox(height: 3.h),
              Text("数据"),
            ],
          ),
        ],
      ),
    );
  }
}
