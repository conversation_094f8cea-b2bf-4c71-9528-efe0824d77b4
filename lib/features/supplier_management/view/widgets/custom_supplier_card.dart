import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';
import 'package:inventory_app_final/features/supplier_management/view/widgets/edit_supplier_dialog.dart';
import 'package:inventory_app_final/features/warehouse_management/view/widgets/edit_warehouse_dialog.dart';

class CustomSupplierCard extends StatefulWidget {
  final String companyName;
  final String name;
  final String phoneNumber;

  const CustomSupplierCard({
    super.key,
    required this.name,
    required this.companyName,
    required this.phoneNumber,
  });

  @override
  State<CustomSupplierCard> createState() => _CustomSupplierCardState();
}

class _CustomSupplierCardState extends State<CustomSupplierCard> {
  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(20.w),
        color: Colors.white,
      ),
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Stack(
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      SizedBox(
                        child: Row(
                          children: [
                            Icon(
                              Icons.co_present_rounded,
                              color: AppColors.primaryColor,
                            ),
                            SizedBox(width: 5.w),
                            Expanded(
                              child: Text(
                                widget.companyName,
                                style: TextStyle(
                                  fontSize: 18.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                                overflow: TextOverflow.ellipsis,
                                maxLines: 1,
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: 5.h),
                      SizedBox(
                        child: Text(
                          "联系人: ${widget.name}",
                          style: TextStyle(fontSize: 13.sp),
                          overflow: TextOverflow.ellipsis,
                          maxLines: 1,
                        ),
                      ),
                      Text(
                        "电话: ${widget.phoneNumber}",
                        style: TextStyle(fontSize: 13.sp),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Positioned(
              right: 1,
              bottom: 1,
              child: Row(
                children: [
                  // 删除按钮
                  GestureDetector(
                    onTap: () {
                      // TODO 检查仓库下是否有物品，如果有则不能删除
                    },
                    child: Container(
                      width: 20.w,
                      height: 20.h,
                      decoration: BoxDecoration(
                        color: Colors.red.shade300,
                        shape: BoxShape.circle,
                      ),
                      alignment: Alignment.center,
                      child: Icon(
                        Icons.delete_rounded,
                        color: Colors.white,
                        size: 15.sp,
                      ),
                    ),
                  ),
                  SizedBox(width: 10.w),
                  // 编辑按钮
                  GestureDetector(
                    onTap: () {
                      showEditSupplierDialog(
                        context: context,
                        initialCompanyName: widget.companyName,
                        initialName: widget.name,
                        initialPhoneNumber: widget.phoneNumber,
                        onNameChanged: (_) {},
                        onConfirm: () {},
                        type: 0,
                      );
                    },
                    child: Container(
                      width: 20.w,
                      height: 20.h,
                      decoration: BoxDecoration(
                        color: AppColors.circleButtonColor,
                        shape: BoxShape.circle,
                      ),
                      alignment: Alignment.center,
                      child: Icon(
                        Icons.edit_rounded,
                        color: Colors.white,
                        size: 15.sp,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
