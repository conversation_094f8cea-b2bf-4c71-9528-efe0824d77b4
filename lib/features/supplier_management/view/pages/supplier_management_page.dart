import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:inventory_app_final/core/theme/app_colors.dart';
import 'package:inventory_app_final/core/widgets/app_body.dart';
import 'package:inventory_app_final/features/supplier_management/view/widgets/custom_supplier_card.dart';
import 'package:inventory_app_final/features/supplier_management/view/widgets/edit_supplier_dialog.dart';
import 'package:inventory_app_final/features/warehouse_management/view/widgets/custom_warehouse_card.dart';
import 'package:inventory_app_final/features/warehouse_management/view/widgets/edit_warehouse_dialog.dart';

class SupplierManagementPage extends StatefulWidget {
  const SupplierManagementPage({super.key});

  @override
  State<SupplierManagementPage> createState() => _SupplierManagementPageState();
}

class _SupplierManagementPageState extends State<SupplierManagementPage> {
  @override
  Widget build(BuildContext context) {
    return AppBody(
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: AppColors.scaffoldBackgroundColor,
          title: Center(
            child: Padding(
              padding: const EdgeInsets.only(right: 10),
              child: Text("供应商管理"),
            ),
          ),
          actions: [
            IconButton(
              onPressed: () {
                showEditSupplierDialog(
                  context: context,
                  initialCompanyName: "",
                  initialName: "",
                  initialPhoneNumber: "",
                  onNameChanged: (_) {},
                  onConfirm: () {},
                  type: 1,
                );
              },
              icon: Icon(Icons.add_rounded, size: 25.w),
            ),
          ],
        ),
        body: Column(
          children: [
            SizedBox(height: 10.h),
            CustomSupplierCard(
              companyName: '广州天天贸易有限公司asdasdsa sadas',
              name: "陈老板",
              phoneNumber: "13812345678",
            ),
            SizedBox(height: 10.h),
            CustomSupplierCard(
              companyName: '啊东莞大胆贸易有限公司GGDSDPPSS',
              name: "煤老板",
              phoneNumber: "13812345678",
            ),
          ],
        ),
      ),
    );
  }
}
