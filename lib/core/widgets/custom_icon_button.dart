import 'package:flutter/material.dart';

class CustomIconButton extends StatelessWidget {
  final double size;
  final Color backgroundColor;
  final Color iconColor;
  final VoidCallback? onTap;
  final IconData icon;

  const CustomIconButton({
    super.key,
    this.size = 40,
    this.backgroundColor = const Color.fromARGB(255, 243, 245, 247),
    this.iconColor = Colors.black54,
    this.onTap,
    required this.icon,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: size,
        height: size,
        decoration: BoxDecoration(
          color: backgroundColor,
          borderRadius: BorderRadius.circular(size / 3), // 自动适配圆角
        ),
        child: Icon(icon, color: iconColor, size: size / 1.5),
      ),
    );
  }
}
