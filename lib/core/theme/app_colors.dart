import 'package:flutter/material.dart';

class AppColors {
  static final Color primaryColor = const Color.fromARGB(255, 36, 176, 171);
  static final Color secondaryColor = const Color.fromARGB(255, 120, 224, 216);
  static final Color dataCardColor = const Color.fromARGB(255, 12, 131, 151);
  static final Color scaffoldBackgroundColor = const Color.fromARGB(
    255,
    243,
    245,
    247,
  );
  static final Color inBoundColor = Colors.pink.shade200;
  static final Color outBoundColor = primaryColor;
  static final Color addColor = Colors.blue.shade200;
  static final Color editColor = Colors.red.shade200;
  static final Color circleButtonColor = const Color.fromARGB(221, 55, 55, 55);

  static final List<Color> strongButtonColors = [
    const Color.fromARGB(180, 231, 76, 60),
    const Color.fromARGB(180, 46, 204, 113),
    const Color.fromARGB(180, 52, 152, 219),
    const Color.fromARGB(180, 155, 89, 182),
    const Color.fromARGB(180, 241, 196, 15),
    const Color.fromARGB(180, 230, 126, 34),
    const Color.fromARGB(180, 26, 188, 156),
    const Color.fromARGB(180, 192, 57, 43),
    const Color.fromARGB(180, 39, 174, 96),
    const Color.fromARGB(180, 41, 128, 185),
    const Color.fromARGB(180, 142, 68, 173),
    const Color.fromARGB(180, 243, 156, 18),
    const Color.fromARGB(180, 211, 84, 0),
    const Color.fromARGB(180, 22, 160, 133),
    const Color.fromARGB(180, 44, 62, 80),
    const Color.fromARGB(180, 127, 140, 141),
    const Color.fromARGB(180, 0, 122, 204),
    const Color.fromARGB(180, 0, 106, 77),
    const Color.fromARGB(180, 128, 0, 128),
    const Color.fromARGB(180, 200, 30, 70),
  ];
}
